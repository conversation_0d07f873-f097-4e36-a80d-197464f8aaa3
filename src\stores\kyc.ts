import { defineStore } from "pinia";
import { getYearsoldWithDate, stringToInt } from "@/utils/core/tools";
import { KYC_STEP } from "@/views/kyc/CONSTANT";
import { getGlobalDialog } from "@/enter/vant";
import router from "@/router";
import { updateInfo } from "@/api/user";
import { useKycMgrStore } from "@/stores/kycMgr";
import { useGlobalStore } from "@/stores/global";

// 生成唯一的 KYC Store Key
function getKycStoreKey(): string {
  // 在 store 初始化时使用基础 key
  // 用户登录后会通过 switchToUserStorage 方法切换到用户特定的存储
  return "kycStore_base";
}

// 详版表单初始值
const initFullFormData = {
  first_name: "",
  middle_name: "",
  last_name: "",
  day: "",
  month: "",
  year: "",
  branch: "",
  nationality: "",
  place_of_birth: "",
  current_address: "",
  permanent_address: "",
  work: "",
  income: "",
  country: "Philippines",
  id_type: "",
  account_type: "",
  account_no: "",
};
// 简版表单初始值
const initSimpleFormData = {
  first_name: "",
  middle_name: "",
  last_name: "",
  day: "",
  month: "",
  year: "",
};

export const useKycStore = defineStore("kyc", {
  state: () => ({
    fullFormData: { ...initFullFormData }, // 详版表单
    simpleFormData: { ...initSimpleFormData }, // 简版表单
    detailDayInputErrTip: "", // 详版-出生年月错误提示
    simpleDayInputErrTip: "", // 简版-出生年月错误提示
    isSameCurrentAddress: true, // 详版-原先地址和当前地址一致
    isGovemmentOfficial: true, // 详版-政府官员
    curStep: KYC_STEP.KYC_STEP_NAME, // 详版-当前步骤
    detailPhotoBase64: "", // 详版-照片base64
    simplePhotoBase64: "", // 简版-照片base64
  }),
  actions: {
    // 上一步
    handlePreStep() {
      const $dialog = getGlobalDialog();
      switch (this.curStep) {
        case KYC_STEP.KYC_STEP_ADDRESS:
          this.curStep = KYC_STEP.KYC_STEP_NAME;
          break;
        case KYC_STEP.KYC_STEP_PHOTO:
          this.curStep = KYC_STEP.KYC_STEP_ADDRESS;
          break;
        case KYC_STEP.KYC_STEP_MEDIA:
          this.curStep = KYC_STEP.KYC_STEP_PHOTO;
          break;
        case KYC_STEP.KYC_STEP_NAME:
          $dialog({
            title: "Tips",
            message: `
              <div style="text-align:center">Are you sure you want to cancel your registration?</div>
            `,
            confirmText: "Confirm",
            cancelText: "Cancel",
            onConfirm: async () => {
              router.replace("/");
            },
          });
          break;
        default:
          break;
      }
    },
    handleSimplePre() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "Tips",
        message: "Are you sure you want to cancel your registration?",
        confirmText: "Confirm",
        cancelText: "Cancel",
        onConfirm: async () => {
          router.back();
        },
      });
    },
    // 下一步
    handleNextStep() {
      switch (this.curStep) {
        case KYC_STEP.KYC_STEP_NAME:
          this.curStep = KYC_STEP.KYC_STEP_ADDRESS;
          break;
        case KYC_STEP.KYC_STEP_ADDRESS:
          this.curStep = KYC_STEP.KYC_STEP_PHOTO;
          break;
        case KYC_STEP.KYC_STEP_PHOTO:
          this.curStep = KYC_STEP.KYC_STEP_MEDIA;
          break;
        case KYC_STEP.KYC_STEP_MEDIA:
          this.handleDetailSubmit();
          break;
        default:
          break;
      }
    },
    // 更新详版照片
    updateDetailPhotoBase64(value: string) {
      this.detailPhotoBase64 = value;
    },
    // 更新简版照片
    updateSimplePhotoBase64(value: string) {
      this.simplePhotoBase64 = value;
    },
    // 同步当前地址到原先地址
    handleSameAddressChecked() {
      if (this.isSameCurrentAddress) {
        // 选中时，同步当前地址到永久地址
        this.fullFormData.permanent_address = this.fullFormData.current_address;
      } else {
        // 取消选中时，清空永久地址
        this.fullFormData.permanent_address = "";
      }
    },
    // 下拉选择赋值
    handleSelectConfirm(field: keyof typeof initFullFormData, data: { value: string }) {
      if (field) {
        this.fullFormData[field] = data.value;
      }
    },
    // 详版表单字段更新
    updateDetailFormData(field: keyof typeof initFullFormData, value: any) {
      if (typeof this.fullFormData !== "object" || this.fullFormData === null) {
        this.fullFormData = { ...initFullFormData };
      }
      let errStr = this.vaildDateInputErr(field, value);
      if (
        !errStr &&
        getYearsoldWithDate(
          this.fullFormData.year,
          this.fullFormData.month,
          this.fullFormData.day
        ) < 21
      ) {
        errStr = "Under 21 years old";
      }
      this.detailDayInputErrTip = errStr;
      this.fullFormData[field] = value;
      // 同步当前地址到原先地址
      if (field === "current_address" && this.isSameCurrentAddress) {
        this.fullFormData.permanent_address = this.fullFormData.current_address;
      }
    },
    // 简版表单字段更新
    updateSimpleFormData(field: keyof typeof initSimpleFormData, value: any) {
      if (typeof this.simpleFormData !== "object" || this.simpleFormData === null) {
        this.simpleFormData = { ...initSimpleFormData };
      }
      let errStr = this.vaildDateInputErr(field, value);
      if (
        !errStr &&
        getYearsoldWithDate(
          this.simpleFormData.year,
          this.simpleFormData.month,
          this.simpleFormData.day
        ) < 21
      ) {
        errStr = "Under 21 years old";
      }
      this.simpleDayInputErrTip = errStr;
      this.simpleFormData[field] = value;
    },
    // 校验日期输入
    vaildDateInputErr(field: string, value: any) {
      let errStr = "";
      if (["day", "month", "year"].includes(field)) {
        if (!value) {
          errStr = "Date of Birth cannot be empty";
        }
      }
      if (field === "day") {
        const day = stringToInt(value);
        if (day < 1 || day > 31) {
          errStr = "Please enter a correct date";
        }
      }
      if (field === "month") {
        const month = stringToInt(value);
        if (month < 1 || month > 12) {
          errStr = "Please enter a correct month";
        }
      }
      if (field === "year") {
        const year = stringToInt(value);
        const nowYear = new Date().getFullYear();
        if (year < 1920 || year > nowYear) {
          errStr = "Please enter a correct year";
        }
      }
      return errStr;
    },
    // 详版表单提交
    async handleDetailSubmit() {
      await updateInfo({
        ...this.fullFormData,
        font_side_url: this.detailPhotoBase64,
      });

      // 更新状态
      useKycMgrStore().fetchKycStatus();

      // reset data
      this.curStep = KYC_STEP.KYC_STEP_NAME;
      this.fullFormData = { ...initFullFormData };
      this.detailPhotoBase64 = "";
      this.detailDayInputErrTip = "";
      this.isSameCurrentAddress = true;
      this.isGovemmentOfficial = true;

      router.replace("/kyc/success");
    },
    // 简版表单提交
    async handleSimpleSubmit() {
      await updateInfo({
        ...this.simpleFormData,
        font_side_url: this.simplePhotoBase64,
      });
      // 更新状态
      useKycMgrStore().fetchKycStatus();

      // reset data
      this.simpleFormData = { ...initSimpleFormData };
      this.simplePhotoBase64 = "";
      this.simpleDayInputErrTip = "";

      router.replace("/kyc/success?simple=1");
    },

    // 获取用户特定的存储 key
    getUserSpecificKey(): string {
      try {
        const globalStore = useGlobalStore();
        const userPhone = globalStore.userInfo?.phone;
        return `kycStore_${userPhone}`;
      } catch (error) {
        console.warn("获取用户信息失败，使用默认 key:", error);
        return "kycStore_default";
      }
    },

    // 切换到用户特定的存储（用户登录后调用）
    switchToUserStorage() {
      const baseKey = "kycStore_base";
      const userKey = this.getUserSpecificKey();
      if (baseKey === userKey) {
        console.log("存储 key 相同，无需切换");
        return;
      }
      try {
        // 1. 获取基础存储的数据
        const baseData = localStorage.getItem(baseKey);
        // 2. 检查用户特定存储是否已有数据
        const userData = localStorage.getItem(userKey);
        if (baseData && !userData) {
          // 3. 如果基础存储有数据，用户存储没有数据，则迁移数据
          localStorage.setItem(userKey, baseData);
          console.log("✅ 数据已从基础存储迁移到用户存储");

          // 4. 清除基础存储的数据
          localStorage.removeItem(baseKey);
          console.log("🗑️ 基础存储数据已清除");
        } else if (userData) {
          // 5. 如果用户存储已有数据，直接使用用户数据
          console.log("📂 用户存储已有数据，直接使用");

          // 清除基础存储的数据
          if (baseData) {
            localStorage.removeItem(baseKey);
            console.log("🗑️ 基础存储数据已清除");
          }
        } else {
          console.log("📝 无数据需要迁移");
        }
      } catch (error) {
        console.error("❌ 切换存储失败:", error);
      }
    },

    // 保存用户特定的数据到 localStorage
    saveUserSpecificData() {
      const key = this.getUserSpecificKey();
      const data = {
        fullFormData: this.fullFormData,
        simpleFormData: this.simpleFormData,
        detailDayInputErrTip: this.detailDayInputErrTip,
        simpleDayInputErrTip: this.simpleDayInputErrTip,
        isSameCurrentAddress: this.isSameCurrentAddress,
        isGovemmentOfficial: this.isGovemmentOfficial,
        curStep: this.curStep,
        detailPhotoBase64: this.detailPhotoBase64,
        simplePhotoBase64: this.simplePhotoBase64,
      };

      try {
        localStorage.setItem(key, JSON.stringify(data));
        console.log("KYC 数据已保存到用户特定存储:", key);
      } catch (error) {
        console.error("保存 KYC 数据失败:", error);
      }
    },

    // 从 localStorage 加载用户特定的数据
    loadUserSpecificData() {
      const key = this.getUserSpecificKey();

      try {
        const savedData = localStorage.getItem(key);
        if (savedData) {
          const data = JSON.parse(savedData);

          // 恢复数据
          this.fullFormData = { ...initFullFormData, ...data.fullFormData };
          this.simpleFormData = { ...initSimpleFormData, ...data.simpleFormData };
          this.detailDayInputErrTip = data.detailDayInputErrTip || "";
          this.simpleDayInputErrTip = data.simpleDayInputErrTip || "";
          this.isSameCurrentAddress = data.isSameCurrentAddress ?? true;
          this.isGovemmentOfficial = data.isGovemmentOfficial ?? true;
          this.curStep = data.curStep || KYC_STEP.KYC_STEP_NAME;
          this.detailPhotoBase64 = data.detailPhotoBase64 || "";
          this.simplePhotoBase64 = data.simplePhotoBase64 || "";

          console.log("KYC 数据已从用户特定存储加载:", key);
        }
      } catch (error) {
        console.error("加载 KYC 数据失败:", error);
      }
    },

    // 清除用户特定的数据
    clearUserSpecificData() {
      const key = this.getUserSpecificKey();

      try {
        localStorage.removeItem(key);
        console.log("KYC 用户特定数据已清除:", key);
      } catch (error) {
        console.error("清除 KYC 数据失败:", error);
      }
    },
  },
  persist: {
    key: getKycStoreKey(),
    storage: window.localStorage,
  },
});
