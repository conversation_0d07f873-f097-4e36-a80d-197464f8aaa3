<template>
  <ZDialog
    v-model="showKycVerifyTip"
    title="KYC Verification"
    :show-close="showCloseBtn"
    confirm-text="Verify Now"
    :on-confirm="handleConfirm"
    :showCancelButton="false"
    @handleClose="handleClose"
  >
    <div class="content">
      <div class="subtitle">{{ subTitle }}</div>
      <div class="desc">{{ desc }}</div>
    </div>
  </ZDialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useKycMgrStore, KYC_STATUS } from "@/stores/kycMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";

const kycMgrStore = useKycMgrStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showKycVerifyTip } = storeToRefs(autoPopMgrStore);
const { kycStatus, kycType, rejectMsg, isFullKyc } = storeToRefs(kycMgrStore);

const subTitle = computed(() => {
  if (kycStatus.value == KYC_STATUS.REJECTED) {
    return "Review failed";
  }
  return "Your account is not yet fully verified";
});

const showCloseBtn = computed(() => {
  if (isFullKyc.value) {
    return false;
  }
  return true;
});

const desc = computed(() => {
  if (kycStatus.value == KYC_STATUS.REJECTED) {
    return rejectMsg.value;
  }
  return "Your access to a certain service on the NUSTAR Online will be restricted.";
});

const handleClose = () => {
  showKycVerifyTip.value = false;
  AutoPopMgr.destroyCurrentPopup();
};

const handleConfirm = async () => {
  // showKycVerifyTip.value = false;
  kycMgrStore.checkPreconditions();
};

watch(
  () => autoPopMgrStore.showKycVerifyTip,
  (newVal) => {
    if (newVal) {
      window["kycDialogSourceLogin"] = false;
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  .title {
    font-size: 18px;
    font-weight: 800px;
    text-align: center;
  }

  .subtitle {
    margin-top: 10px;
    font-size: 14px;
    text-align: center;
  }

  .desc {
    margin-top: 10px;
    font-size: 14px;
    color: #666;
    text-align: center;
  }
}
</style>
