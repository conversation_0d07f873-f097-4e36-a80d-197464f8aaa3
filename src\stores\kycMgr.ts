import { defineStore } from "pinia";
import { getUserStatus, getKycBonus, getUserKyc } from "@/api/user";
import { useGlobalStore } from "@/stores/global";
import { showToast } from "vant";
import { getGlobalDialog } from "@/enter/vant";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import router from "@/router";

// KYC 验证场景
export enum KYC_SCENE {
  Login = 0, //登录场景
  Withdraw, //提现 验证
  MyCenter, //个人中心
  GoThirdGame, //打开第三方游戏
  UNKNOWN = -1, // 未知
}

// KYC 认证状态
export enum KYC_STATUS {
  NO_VERIFY = 0, // 未验证
  COMPLETE = 1, // 已认证
  REVIEWING = 2, // 审核中
  REJECTED = 3, // 已拒绝
  UNKNOWN = -1, // 未知状态
}

// KYC 认证类型
export enum KYC_TYPE {
  CLOSE = 0, // 关闭状态
  FULL = 1, // 完整版
  SIMPLE = 2, // 简版
  STANDING = 3, // 听证会版
  UNKNOWN = -1, // 未知状态
}

// KYC 奖励信息接口
export interface KYC_BONUS_INFO {
  is_kyc_completed: number; // 是否完成 KYC (1: 已完成, 0: 未完成)
  kyc_completed_reward: string; // KYC 完成奖励金额
  status: number; // 显示状态 (1: 显示, 0: 不显示)
}

// 初始状态值
const initValue = {
  kycStatus: KYC_STATUS.UNKNOWN,
  kycType: KYC_TYPE.CLOSE,
  rejectMsg: "You are younger than 21.",
  kycVerifyCallback: undefined as ((state: boolean) => void) | undefined,
  //进入验证的 入口状态
  inScene: KYC_SCENE.UNKNOWN,
  // 手机号绑定
  showVerifyPreconditionsDialog: false,
  kycBonusInfo: {
    is_kyc_completed: 0, // 默认未完成
    kyc_completed_reward: "0", // 默认无奖励
    status: 0, // 默认不显示
  } as KYC_BONUS_INFO,
  kycFillInData: {},
};

export const useKycMgrStore = defineStore("kycMgr", {
  state: () => ({
    ...initValue,
  }),

  getters: {
    CHANEL_TYPE() {
      const globalStore = useGlobalStore();
      return globalStore.channel ?? "";
    },
    // 是否显示 KYC奖励 提示
    showKycCompletedRewardTip: (state) => {
      return (
        state.kycBonusInfo.status === 1 &&
        state.kycBonusInfo.is_kyc_completed === 0 &&
        Number(state.kycBonusInfo.kyc_completed_reward) > 0
      );
    },

    // 是否已完成 KYC
    isKycCompleted: (state) => {
      return state.kycStatus === KYC_STATUS.COMPLETE;
    },

    // 是否正在审核中
    isKycReviewing: (state) => {
      return state.kycStatus === KYC_STATUS.REVIEWING;
    },

    // 是否被拒绝
    isKycRejected: (state) => {
      return state.kycStatus === KYC_STATUS.REJECTED;
    },

    // 简版
    isSimpleKyc: (state) => {
      return state.kycType === KYC_TYPE.SIMPLE;
    },
    // 详版
    isFullKyc: (state) => {
      return state.kycType === KYC_TYPE.FULL;
    },
  },

  actions: {
    // 私有方法：获取 globalStore 实例
    _getGlobalStore() {
      return useGlobalStore();
    },

    // 私有方法：检查用户是否已登录
    _isLoggedIn(): boolean {
      return !!this._getGlobalStore().token;
    },
    // 获取 KYC 状态
    async fetchKycStatus(callback?: (response: any) => void) {
      if (!this._isLoggedIn()) return;
      try {
        // const res = {
        //   status: KYC_STATUS.NO_VERIFY,
        //   is_full: KYC_TYPE.FULL,
        //   reject: "You are younger than 21.",
        // };
        const res = await getUserStatus({});

        this.kycStatus = Number(res.status);
        this.kycType = Number(res.is_full);
        this.rejectMsg = res.reject || this.rejectMsg;

        callback?.(res);
        return res;
      } catch (error) {
        this.kycStatus = KYC_STATUS.UNKNOWN;
        this.kycType = KYC_TYPE.CLOSE;
      }
    },
    // 刷新 KYC 状态
    async refreshKycStatus() {
      if (!this._isLoggedIn()) return;
      try {
        const res = await getUserStatus({});
        this.kycStatus = Number(res.status);
        this.kycType = Number(res.is_full);
        this.rejectMsg = res.reject || this.rejectMsg;

        //已拒绝
        if (this.kycStatus === KYC_STATUS.REJECTED) {
          this.rejectPop();
          return;
        }
        //审核中
        if (this.kycStatus === KYC_STATUS.REVIEWING) {
          this.reviewPop();
          return;
        }
        //审核完成
        if (this.kycStatus === KYC_STATUS.COMPLETE) {
          showToast("KYC Verification Certification Passed");
          return;
        }
      } catch (error) {}
    },

    // 获取 KYC 认证奖励信息
    async fetchKycBonus() {
      if (!this._isLoggedIn()) return;
      try {
        const res = await getKycBonus({});
        this.kycBonusInfo = {
          is_kyc_completed: Number(res.is_kyc_completed),
          kyc_completed_reward: String(res.kyc_completed_reward),
          status: Number(res.status),
        };
      } catch (error) {
        console.error("获取 KYC 奖励信息失败:", error);
        // 保持默认值
      }
    },

    // 获取 KYC 填写数据
    async fetchUserKyc() {
      if (!this._isLoggedIn()) return;
      try {
        const res = await getUserKyc({});
        this.kycFillInData = res;
      } catch (error) {
        console.error("获取 KYC 奖励信息失败:", error);
        // 保持默认值
      }
    },

    // 重置 KYC 状态
    resetKycState() {
      this.kycStatus = KYC_STATUS.UNKNOWN;
      this.kycType = KYC_TYPE.CLOSE;
      this.kycBonusInfo = { ...initValue.kycBonusInfo };
    },

    // 更新 KYC 状态（用于其他地方手动更新）
    updateKycState(state: KYC_STATUS, simple?: KYC_TYPE) {
      this.kycStatus = state;
      if (simple !== undefined) {
        this.kycType = simple;
      }
    },

    backToTarget(state = false) {
      this.kycVerifyCallback?.(state);
      this.kycVerifyCallback = undefined;
    },
    /**
     * 入口方法：校验 KYC
     */
    async verifyKyc(inType: KYC_SCENE, callback?: (state: boolean) => void) {
      this.kycVerifyCallback = callback;
      this.inScene = inType;

      if (this.CHANEL_TYPE !== CHANEL_TYPE.WEB) {
        this.backToTarget(true);
        return;
      }
      if (this.kycStatus === KYC_STATUS.UNKNOWN) {
        await this.fetchKycStatus(() => {
          this.goNext();
        });
      } else {
        this.goNext();
      }
    },

    async goNext() {
      // 关闭KYC 验证
      if (this.kycType === KYC_TYPE.CLOSE) {
        this.backToTarget(true);
        return;
      }
      // 简版也放过
      if (this.kycType === KYC_TYPE.SIMPLE) {
        this.backToTarget(true);
        return;
      }
      //未验证
      if (this.kycStatus === KYC_STATUS.NO_VERIFY) {
        this.noVerifyPop();
        return;
      }
      //已拒绝
      if (this.kycStatus === KYC_STATUS.REJECTED) {
        this.rejectPop();
        return;
      }
      //审核中
      if (this.kycStatus === KYC_STATUS.REVIEWING) {
        switch (this.inScene) {
          case KYC_SCENE.GoThirdGame:
            this.backToTarget(true);
            break;
          case KYC_SCENE.Withdraw:
            //提现 只在完整版 弹窗提示！
            if (this.kycType === KYC_TYPE.FULL) {
              this.backToTarget(false);
            }
            this.reviewPop();
            break;
        }
        return;
      }
      this.backToTarget(true);
    },

    /**
     * 未提交
     */
    noVerifyPop() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "KYC Verification",
        message: `<div style="color:#222;text-align:center;">Your account is not yet fully verified</div>`,
        describe: `<div style="color:#999;text-align:center;">Your access to a certain service on the NUSTAR Online will be restricted.</div>`,
        confirmText: "Verify Now",
        onConfirm: async () => {
          //验证是否 绑定手机号
          this.checkPreconditions();
        },
        onCancel: () => {
          if (this.kycType === KYC_TYPE.SIMPLE) {
            this.backToTarget(true);
          } else {
            this.backToTarget(false);
            // 退出登录
            this.logoutGame();
          }
        },
      });
    },

    /**
     * 审核拒绝
     */
    rejectPop() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "KYC Verification",
        message: `<div style="text-align:center">Review failed</div>`,
        describe: this.rejectMsg,
        onConfirm: async () => {
          //拒绝之后不用验证 输入手机号之类的 直接弹出kyc
          this.openKycVerify();
        },
        onCancel: () => {
          if (this.kycType === KYC_TYPE.SIMPLE) {
            this.backToTarget(true);
          } else {
            this.backToTarget(false);
            // 退出登录
            this.logoutGame();
          }
        },
      });
    },

    /**
     *  审核中
     */
    reviewPop() {
      const $dialog = getGlobalDialog();
      $dialog({
        title: "KYC Verification",
        message: `<div style="margin-bottom:10px;text-align:center;color:#000">Estimated review time: 1 day.</div> `,
        describe: ` <div style="color:#666;text-align:center;">
        <div>Thank you for providing the required information. After the review is completed, we will notify you via an internal message.</div></div>
        <div  style="color:#666;text-align:center;margin-top:10px;">We are reviewing your application.</div>
        `,
        confirmText: "Refresh",
        onConfirm: async () => {
          //  刷新
          await this.refreshKycStatus();
        },
        onCancel: () => {},
      });
    },

    /**
     * 验证前置条件
     */
    checkPreconditions() {
      const globalStore = useGlobalStore();
      const user = globalStore.userInfo;
      if (!!user?.phone && !!user?.login_password) {
        // 如果 有手机号、登录密码 直接开始kyc
        this.openKycVerify();
      } else {
        this.showVerifyPreconditionsDialog = true;
      }
    },

    // 验证前置条件成功
    handleVerifyPreconditionsSuccess(isAllSuccess: boolean) {
      if (isAllSuccess) {
        this.showVerifyPreconditionsDialog = false;
        this.openKycVerify();
      } else {
        this.showVerifyPreconditionsDialog = true;
      }
    },

    /**
     * 打开 KYC 验证弹窗
     */
    openKycVerify() {
      if (this.CHANEL_TYPE == CHANEL_TYPE.MAYA) {
        return;
      }
      // 审核拒绝或者 审核完成后 不再弹出
      if (this.kycStatus === KYC_STATUS.COMPLETE || this.kycStatus === KYC_STATUS.REVIEWING) return;
      // 跳转kyc 验证弹窗
      if (this.kycType === KYC_TYPE.SIMPLE) {
        router.push(`/kyc/simple-form`);
      } else if (this.kycType === KYC_TYPE.FULL) {
        router.push(`/kyc/full-form`);
      } else if (this.kycType === KYC_TYPE.CLOSE) {
        router.push(`/kyc/simple-form`);
      }
    },

    logoutGame() {
      const globalStore = useGlobalStore();
      globalStore.loginOut();
    },

    //KYC 展示
    isNeedShowPopKYC() {
      if (!this._isLoggedIn()) return false;
      // 如果是非web 渠道，不弹出
      if (this.CHANEL_TYPE !== CHANEL_TYPE.WEB) {
        return false;
      }
      // 未认证、审核拒绝
      if ([KYC_STATUS.NO_VERIFY, KYC_STATUS.REJECTED].includes(this.kycStatus)) {
        // 简版、登录页过来会弹窗
        if (this.kycType == KYC_TYPE.SIMPLE && window["kycDialogSourceLogin"]) {
          return true;
        }
        if (this.kycType == KYC_TYPE.FULL) {
          return true;
        }
      }
      return false;
    },
  },
});
